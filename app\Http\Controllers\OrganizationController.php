<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use App\Models\OrganizationRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $organizations = Organization::with(['creator', 'activeMembers'])
            ->where('status', 'active')
            ->withCount('activeMembers')
            ->orderBy('name')
            ->paginate(12);

        return view('organizations.index', compact('organizations'));
    }

    /**
     * Display user's organizations
     */
    public function my()
    {
        $user = auth()->user();

        $myOrganizations = $user->organizations()
            ->with(['creator'])
            ->withCount('activeMembers')
            ->wherePivot('status', 'active')
            ->orderBy('name')
            ->get();

        $pendingInvitations = $user->organizations()
            ->with(['creator'])
            ->withCount('activeMembers')
            ->wherePivot('status', 'pending')
            ->orderBy('name')
            ->get();

        return view('organizations.my', compact('myOrganizations', 'pendingInvitations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();

        // Admins can create organizations without requests
        if ($user->isAdmin()) {
            return view('organizations.create');
        }

        // Regular users need an approved request
        $approvedRequest = $user->organizationRequests()
            ->approved()
            ->where('organization_created', false)
            ->first();

        if (!$approvedRequest) {
            return redirect()->route('organization-requests.index')
                ->with('error', 'You need an approved organization request to create an organization. Please submit a request first.');
        }

        return view('organizations.create', compact('approvedRequest'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $approvedRequest = null;

        // Check if user needs an approved request (non-admins)
        if (!$user->isAdmin()) {
            $approvedRequest = $user->organizationRequests()
                ->approved()
                ->where('organization_created', false)
                ->first();

            if (!$approvedRequest) {
                return back()->withErrors(['name' => 'You need an approved organization request to create an organization.']);
            }
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:organizations,name',
            'description' => 'required|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        // For non-admins, validate that the organization name matches the approved request
        if ($approvedRequest && $validated['name'] !== $approvedRequest->organization_name) {
            return back()->withErrors([
                'name' => 'Organization name must match your approved request: ' . $approvedRequest->organization_name
            ]);
        }

        $validated['slug'] = Str::slug($validated['name']);
        $validated['created_by'] = auth()->id();
        $validated['status'] = 'active'; // Organizations created from approved requests are automatically active

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('organizations/logos', 'public');
        }

        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('organizations/covers', 'public');
        }

        $organization = Organization::create($validated);

        // Add creator as president
        $organization->members()->attach(auth()->id(), [
            'role' => 'president',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // Mark the request as used (if applicable)
        if ($approvedRequest) {
            $approvedRequest->markOrganizationCreated($organization);
        }

        return redirect()->route('organizations.show', $organization)
            ->with('success', 'Organization created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Organization $organization)
    {
        // If in page mode, redirect to page view
        if ($organization->is_page_mode) {
            return $this->page($organization);
        }

        $organization->load([
            'creator',
            'activeMembers' => function ($query) {
                $query->orderBy('organization_members.role', 'desc')
                      ->orderBy('organization_members.joined_at');
            },
            'posts' => function ($query) {
                $query->published()
                      ->approved()
                      ->with(['user', 'likes', 'comments.user', 'comments.reactions', 'comments.replies.user', 'comments.replies.reactions', 'fileAttachments'])
                      ->latest('published_at')
                      ->limit(10);
            }
        ]);

        $userMembership = null;
        if (auth()->check()) {
            $userMembership = $organization->members()
                ->where('user_id', auth()->id())
                ->first();
        }

        return view('organizations.show', compact('organization', 'userMembership'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Organization $organization)
    {
        // Check if user can edit this organization
        if (!$this->canManageOrganization($organization)) {
            abort(403, 'Unauthorized to edit this organization.');
        }

        return view('organizations.edit', compact('organization'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Organization $organization)
    {
        // Check if user can edit this organization
        if (!$this->canManageOrganization($organization)) {
            abort(403, 'Unauthorized to update this organization.');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('organizations')->ignore($organization)],
            'description' => 'required|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('organizations/logos', 'public');
        }

        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('organizations/covers', 'public');
        }

        $organization->update($validated);

        return redirect()->route('organizations.show', $organization)
            ->with('success', 'Organization updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Organization $organization)
    {
        // Only admins or organization creator can delete
        if (!auth()->user()->isAdmin() && $organization->created_by !== auth()->id()) {
            abort(403, 'Unauthorized to delete this organization.');
        }

        $organization->delete();

        return redirect()->route('organizations.index')
            ->with('success', 'Organization deleted successfully!');
    }

    /**
     * Join an organization
     */
    public function join(Organization $organization)
    {
        $user = auth()->user();

        // Check if already a member
        if ($organization->members()->where('user_id', $user->id)->exists()) {
            return back()->with('error', 'You are already a member of this organization.');
        }

        // Add as pending member
        $organization->members()->attach($user->id, [
            'role' => 'member',
            'status' => 'pending',
        ]);

        return back()->with('success', 'Join request sent! Waiting for approval.');
    }

    /**
     * Leave an organization
     */
    public function leave(Organization $organization)
    {
        $user = auth()->user();

        $membership = $organization->members()->where('user_id', $user->id)->first();

        if (!$membership) {
            return back()->with('error', 'You are not a member of this organization.');
        }

        // Presidents cannot leave unless they transfer leadership
        if ($membership->pivot->role === 'president') {
            return back()->with('error', 'Presidents must transfer leadership before leaving.');
        }

        $organization->members()->detach($user->id);

        return back()->with('success', 'You have left the organization.');
    }

    /**
     * Follow an organization (page mode)
     */
    public function follow(Organization $organization)
    {
        $user = auth()->user();

        // Check if already following
        if ($organization->isFollowedBy($user)) {
            return back()->with('error', 'You are already following this organization.');
        }

        $organization->followers()->attach($user->id);

        return back()->with('success', 'You are now following this organization!');
    }

    /**
     * Unfollow an organization (page mode)
     */
    public function unfollow(Organization $organization)
    {
        $user = auth()->user();

        // Check if following
        if (!$organization->isFollowedBy($user)) {
            return back()->with('error', 'You are not following this organization.');
        }

        $organization->followers()->detach($user->id);

        return back()->with('success', 'You have unfollowed this organization.');
    }

    /**
     * Toggle page mode for organization
     */
    public function togglePageMode(Organization $organization)
    {
        // Check permissions
        if (!$this->canManageOrganization($organization)) {
            abort(403, 'You do not have permission to modify this organization.');
        }

        $organization->update([
            'is_page_mode' => !$organization->is_page_mode
        ]);

        $mode = $organization->is_page_mode ? 'page' : 'organization';
        return back()->with('success', "Organization switched to {$mode} mode successfully!");
    }

    /**
     * Show organization followers (page mode)
     */
    public function followers(Organization $organization)
    {
        if (!$organization->is_page_mode) {
            return redirect()->route('organizations.show', $organization);
        }

        $followers = $organization->followers()
            ->orderBy('organization_followers.created_at', 'desc')
            ->paginate(20);

        return view('organizations.followers', compact('organization', 'followers'));
    }

    /**
     * Show organization page in page mode
     */
    public function page(Organization $organization)
    {
        // Redirect to regular view if not in page mode
        if (!$organization->is_page_mode) {
            return redirect()->route('organizations.show', $organization);
        }

        $organization->load([
            'creator',
            'officers',
            'posts' => function ($query) {
                $query->published()
                      ->approved()
                      ->with(['user', 'likes', 'comments', 'fileAttachments'])
                      ->latest('published_at')
                      ->limit(10);
            }
        ]);

        $userFollowing = null;
        $userMembership = null;

        if (auth()->check()) {
            $userFollowing = $organization->isFollowedBy(auth()->user());
            $userMembership = $organization->members()
                ->where('user_id', auth()->id())
                ->first();
        }

        $followersCount = $organization->followers()->count();

        return view('organizations.page', compact(
            'organization',
            'userFollowing',
            'userMembership',
            'followersCount'
        ));
    }

    /**
     * Check if user can manage organization
     */
    private function canManageOrganization(Organization $organization): bool
    {
        $user = auth()->user();

        // Admins can manage any organization
        if ($user->isAdmin()) {
            return true;
        }

        // Organization creator can manage
        if ($organization->created_by === $user->id) {
            return true;
        }

        // Officers and presidents can manage
        $membership = $organization->members()->where('user_id', $user->id)->first();
        if ($membership && in_array($membership->pivot->role, ['officer', 'president'])) {
            return true;
        }

        return false;
    }

    /**
     * Show officer management page
     */
    public function officers(Organization $organization)
    {
        // Check if user can manage officers (only president can)
        if (!$this->canManageOfficers($organization)) {
            abort(403, 'Only the president can manage officers.');
        }

        $organization->load([
            'activeMembers' => function ($query) {
                $query->orderBy('organization_members.role', 'desc')
                      ->orderBy('organization_members.joined_at');
            }
        ]);

        $availableRoles = [
            'vice_president' => 'Vice President',
            'secretary' => 'Secretary',
            'treasurer' => 'Treasurer',
            'officer' => 'Officer',
        ];

        // Prepare member data for JavaScript
        $memberData = $organization->activeMembers
            ->where('pivot.role', 'member')
            ->map(function($member) {
                return [
                    'id' => $member->id,
                    'name' => $member->name,
                    'email' => $member->email,
                    'avatar_url' => $member->getAvatarUrl(32)
                ];
            })->values();

        return view('organizations.officers', compact('organization', 'availableRoles', 'memberData'));
    }

    /**
     * Add or update an officer
     */
    public function addOfficer(Request $request, Organization $organization)
    {
        // Check if user can manage officers
        if (!$this->canManageOfficers($organization)) {
            abort(403, 'Only the president can manage officers.');
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|in:vice_president,secretary,treasurer,officer',
            'custom_role_title' => 'nullable|string|max:100',
        ]);

        // If custom_role_title is provided but role is empty, default to 'officer'
        if (!empty($validated['custom_role_title']) && empty($validated['role'])) {
            $validated['role'] = 'officer';
        }

        // Validate that custom roles have a title
        if ($validated['role'] === 'officer' && empty($validated['custom_role_title'])) {
            // Check if this is a predefined officer role or truly custom
            $predefinedRoles = ['vice_president', 'secretary', 'treasurer'];
            if (!in_array($request->input('role'), $predefinedRoles)) {
                return back()->withErrors([
                    'custom_role_title' => 'Custom role title is required for officer positions.'
                ]);
            }
        }

        // Check if user is already a member
        $membership = $organization->members()->where('user_id', $validated['user_id'])->first();

        if (!$membership) {
            return back()->with('error', 'User must be a member of the organization first.');
        }

        // Update the member's role
        $organization->members()->updateExistingPivot($validated['user_id'], [
            'role' => $validated['role'],
            'custom_role_title' => $validated['custom_role_title'] ?: null,
        ]);

        $user = User::find($validated['user_id']);
        $roleName = $validated['custom_role_title'] ?: match($validated['role']) {
            'vice_president' => 'Vice President',
            'secretary' => 'Secretary',
            'treasurer' => 'Treasurer',
            'officer' => 'Officer',
        };

        // If it's a custom role without a predefined title, use the custom title
        if ($validated['role'] === 'officer' && !empty($validated['custom_role_title'])) {
            $roleName = $validated['custom_role_title'];
        }

        return back()->with('success', "{$user->name} has been appointed as {$roleName}.");
    }

    /**
     * Remove an officer (demote to member)
     */
    public function removeOfficer(Request $request, Organization $organization)
    {
        // Check if user can manage officers
        if (!$this->canManageOfficers($organization)) {
            abort(403, 'Only the president can manage officers.');
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        // Cannot remove the president
        $membership = $organization->members()->where('user_id', $validated['user_id'])->first();
        if ($membership && $membership->pivot->role === 'president') {
            return back()->with('error', 'Cannot remove the president from their role.');
        }

        // Update the member's role to regular member
        $organization->members()->updateExistingPivot($validated['user_id'], [
            'role' => 'member',
            'custom_role_title' => null,
        ]);

        $user = User::find($validated['user_id']);
        return back()->with('success', "{$user->name} has been demoted to regular member.");
    }

    /**
     * Check if user can manage officers (only president can)
     */
    private function canManageOfficers(Organization $organization): bool
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            return true;
        }

        $membership = $organization->members()->where('user_id', $user->id)->first();
        return $membership && $membership->pivot->role === 'president';
    }

    /**
     * Show organization dashboard for officers
     */
    public function dashboard(Organization $organization)
    {
        $user = auth()->user();

        // Check if user is an officer or admin
        if (!$user->isAdmin() && !$organization->officers()->where('user_id', $user->id)->exists()) {
            abort(403, 'Only officers can access the organization dashboard.');
        }

        // Get dashboard statistics
        $stats = [
            'total_members' => $organization->activeMembers->count(),
            'total_posts' => $organization->posts()->count(),
            'total_likes' => $organization->posts()->withCount('reactions')->get()->sum('reactions_count'),
            'total_comments' => $organization->posts()->withCount('comments')->get()->sum('comments_count'),
            'pending_members' => $organization->members()->where('organization_members.status', 'pending')->count(),
        ];

        // Recent member activity (last 30 days)
        $recentMembers = $organization->members()
            ->where('organization_members.joined_at', '>=', now()->subDays(30))
            ->orderBy('organization_members.joined_at', 'desc')
            ->take(10)
            ->get();

        // Recent posts with engagement
        $recentPosts = $organization->posts()
            ->with(['user', 'reactions', 'comments'])
            ->withCount(['reactions', 'comments'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Monthly growth data (last 6 months)
        $monthlyGrowth = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();

            $monthlyGrowth[] = [
                'month' => $date->format('M Y'),
                'new_members' => $organization->members()
                    ->whereBetween('organization_members.joined_at', [$monthStart, $monthEnd])
                    ->count(),
                'new_posts' => $organization->posts()
                    ->whereBetween('created_at', [$monthStart, $monthEnd])
                    ->count(),
            ];
        }

        return view('organizations.dashboard', compact(
            'organization',
            'stats',
            'recentMembers',
            'recentPosts',
            'monthlyGrowth'
        ));
    }
}
